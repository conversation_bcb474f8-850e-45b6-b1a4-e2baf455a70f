import * as admin from "firebase-admin";
import {
  logResetGiftServiceStarted,
  logBotTokenNotConfigured,
  logInvalidBotToken,
  logUserNotFound,
  logOrderNotFound,
  logOrderDataCorrupted,
  logOrderNotCancelled,
  logUserNotSeller,
  logGiftResetSuccess,
} from "./reset-gift-on-cancelled-order.logger";
import {
  throwBotTokenNotConfiguredError,
  throwInvalidBotTokenError,
  throwUserNotFoundError,
  throwOrderNotFoundError,
  throwOrderDataCorruptedError,
  throwOrderNotCancelledError,
  throwUserNotSellerError,
  handleResetGiftServiceError,
} from "./reset-gift-on-cancelled-order.error-handler";
import {
  APP_USERS_COLLECTION,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
} from "../mikerudenko/marketplace-shared";
import { markGiftAsWithdrawn } from "../services/gift-service";

interface ResetGiftRequest {
  userId: string;
  botToken: string;
  orderId: string;
}

interface ResetGiftResponse {
  success: boolean;
  message: string;
}

export const resetGiftOnCancelledOrder = async (
  request: ResetGiftRequest
): Promise<ResetGiftResponse> => {
  const { userId, botToken, orderId } = request;

  try {
    logResetGiftServiceStarted({ userId, orderId });

    // 1. Validate bot token
    const expectedBotToken = process.env.BOT_TOKEN;
    if (!expectedBotToken) {
      logBotTokenNotConfigured({ userId, orderId });
      throwBotTokenNotConfiguredError();
    }

    if (botToken !== expectedBotToken) {
      logInvalidBotToken({ userId, orderId });
      throwInvalidBotTokenError();
    }

    // 2. Validate user exists
    // augment-refactor check if current user has telegram id
    const userDoc = await admin
      .firestore()
      .collection(APP_USERS_COLLECTION)
      .doc(userId)
      .get();

    if (!userDoc.exists) {
      logUserNotFound({ userId, orderId });
      throwUserNotFoundError();
    }

    // 3. Validate order exists and is cancelled
    const orderDoc = await admin
      .firestore()
      .collection(ORDERS_COLLECTION_NAME)
      .doc(orderId)
      .get();

    if (!orderDoc.exists) {
      logOrderNotFound({ userId, orderId });
      throwOrderNotFoundError();
    }

    const orderData = orderDoc.data();
    if (!orderData) {
      logOrderDataCorrupted({ userId, orderId });
      throwOrderDataCorruptedError();
    }

    // 4. Validate order status is cancelled
    if (orderData.status !== OrderStatus.CANCELLED) {
      logOrderNotCancelled({
        userId,
        orderId,
        orderStatus: orderData.status,
      });
      throwOrderNotCancelledError();
    }

    // 5. Validate user is the seller of the order
    if (orderData.seller_tg_id !== userId) {
      logUserNotSeller({
        userId,
        orderId,
        orderSellerId: orderData.seller_tg_id,
      });
      throwUserNotSellerError();
    }

    // 6. Reset the gift field and giftId to null, mark gift as withdrawn if exists
    const updateData: any = {
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    // If order has giftId, mark the gift as withdrawn
    if (orderData.giftId) {
      await markGiftAsWithdrawn(orderData.giftId);
    }

    await admin
      .firestore()
      .collection(ORDERS_COLLECTION_NAME)
      .doc(orderId)
      .update(updateData);

    logGiftResetSuccess({ userId, orderId });

    return {
      success: true,
      message: "Gift field reset successfully",
    };
  } catch (error) {
    handleResetGiftServiceError(error, { userId, orderId });
  }
};
