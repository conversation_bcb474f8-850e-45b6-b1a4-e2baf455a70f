import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import { SellerOrderFunctionService } from "./seller-order-function.service";
import {
  logCreateOrderError,
  logPurchaseError,
} from "./seller-order-function.logger";
import { SellerOrderFunctionErrorHandler } from "./seller-order-function.error-handler";

export const createOrderAsSeller = onCall<{
  sellerId: string;
  collectionId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const { sellerId, collectionId, price } = request.data;

  try {
    await SellerOrderFunctionService.validateCreateOrderRequest(request, {
      sellerId,
      collectionId,
      price,
    });

    const result = await SellerOrderFunctionService.createSellerOrder({
      sellerId,
      collectionId,
      price,
    });

    return result;
  } catch (error) {
    logCreateOrderError({
      error,
      sellerId,
      operation: "seller_order_creation",
      requestData: { collectionId, price },
    });

    SellerOrderFunctionErrorHandler.throwCreateOrderError(
      (error as any).message
    );
  }
});

export const makePurchaseAsSeller = onCall<{
  sellerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { sellerId, orderId } = request.data;

  try {
    // Validate request
    await SellerOrderFunctionService.validatePurchaseRequest(request, {
      sellerId,
      orderId,
    });

    const result = await SellerOrderFunctionService.processSellerPurchase({
      sellerId,
      orderId,
    });

    return result;
  } catch (error) {
    logPurchaseError({
      error,
      operation: "purchase_as_seller",
      requestData: request.data,
      userId: request.auth?.uid,
    });

    SellerOrderFunctionErrorHandler.throwPurchaseError((error as any).message);
  }
});
