import * as admin from "firebase-admin";
import { UserType } from "../../mikerudenko/marketplace-shared";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validatePurchaseParams,
  validateSellerOwnership,
  getUserData,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { validateSellerCreatedOrdersLimit } from "../../services/order-validation-service";
import { processPurchase } from "../../services/purchase-flow-service";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";

export interface CreateOrderAsSellerParams {
  sellerId: string;
  collectionId: string;
  price: number;
}

export interface MakePurchaseAsSellerParams {
  sellerId: string;
  orderId: string;
}

export class SellerOrderFunctionService {
  static async validateCreateOrderRequest(
    request: any,
    params: CreateOrderAsSellerParams
  ): Promise<any> {
    const authRequest = requireAuthentication(request);
    validateOrderCreationParams(params, UserType.SELLER);
    validateSellerOwnership(authRequest, params.sellerId);

    // Validate user has telegram ID for order operations
    const user = await getUserData(authRequest.auth.uid);
    validateTelegramIdForOrderOperation(user, "create orders");

    return authRequest;
  }

  static async validatePurchaseRequest(
    request: any,
    params: MakePurchaseAsSellerParams
  ): Promise<any> {
    const authRequest = requireAuthentication(request);
    validatePurchaseParams(params, UserType.SELLER);
    validateSellerOwnership(authRequest, params.sellerId);

    // Validate user has telegram ID for purchase operations
    const user = await getUserData(authRequest.auth.uid);
    validateTelegramIdForOrderOperation(user, "make purchases");

    return authRequest;
  }

  static async createSellerOrder(
    params: CreateOrderAsSellerParams
  ): Promise<any> {
    const db = admin.firestore();
    const { sellerId, collectionId, price } = params;

    // Validate that seller doesn't have too many orders with "created" status
    await validateSellerCreatedOrdersLimit(db, sellerId, collectionId);

    return await createOrder(db, {
      userId: sellerId,
      collectionId,
      price,
      gift: null,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });
  }

  static async processSellerPurchase(
    params: MakePurchaseAsSellerParams
  ): Promise<any> {
    const db = admin.firestore();
    const { sellerId, orderId } = params;

    return await processPurchase(db, {
      userId: sellerId,
      orderId,
      userType: UserType.SELLER,
    });
  }
}
