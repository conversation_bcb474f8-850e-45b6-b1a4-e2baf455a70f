import * as admin from "firebase-admin";
import {
  APP_USERS_COLLECTION,
  AppDate,
  GiftFromBot,
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
  UserEntity,
  formatDateToFirebaseTimestamp,
} from "../../mikerudenko/marketplace-shared";
import { verifyBotToken } from "../../services/bot-auth-service/bot-auth-service";
import { createGift } from "../../services/gift-service";
import { transferNetAmountToSeller } from "../../services/purchase-fee-processing-service";
import { safeMultiply } from "../../utils";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwInvalidOrderId,
  throwInvalidOrderStatus,
  throwOrderNotFound,
} from "./bot-order-function.error-handler";
import {
  queryPaidOrdersAwaitingGift,
  queryCreatedOrdersNeedingActivation,
  queryCancelledOrders,
  queryBuyOrders,
  convertDocsToOrders,
} from "../../services/common/order-query.service";
import {
  resolveUserId,
  getUserTelegramIds,
} from "../../services/common/user-lookup.service";
import { filterOrdersByMarketCollections } from "../../services/common/collection-filter.service";
import { processCancelledOrdersWithGifts } from "./services/cancelled-orders.service";

// Augment-refactor, we have this validation in other bot specific function, adhere dry principle
export function validateBotToken(botToken?: string): void {
  if (!botToken) {
    throwBotTokenRequired();
  }

  if (!verifyBotToken(botToken)) {
    throwInvalidBotToken();
  }
}

export function validateOrderId(orderId?: string): void {
  if (!orderId) {
    throwInvalidOrderId();
  }
}

export async function getOrderById(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  // augment-refactor this piece of code should be taken from order-query-service
  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    return {
      success: false,
      order: null,
      message: "Order not found.",
    };
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Get telegram IDs for buyer and seller
  const { buyer_tg_id, seller_tg_id } = await getUserTelegramIds(order);

  return {
    success: true,
    order: {
      ...order,
      buyer_tg_id,
      seller_tg_id,
    },
    message: "Order retrieved successfully.",
  };
}

export async function getUserOrders(params: {
  userId?: string;
  tgId?: string;
  botToken: string;
}) {
  const { userId, tgId, botToken } = params;

  validateBotToken(botToken);

  // Resolve user ID from either userId or tgId
  const userLookupResult = await resolveUserId({ userId, tgId });

  if (!userLookupResult.success) {
    return {
      success: false,
      orders: [],
      sellOrders: [],
      paidOrdersAwaitingGift: [],
      createdOrdersNeedingActivation: [],
      cancelledOrdersWithGifts: [],
      buyOrders: [],
      count: 0,
      sellOrdersCount: 0,
      paidOrdersAwaitingGiftCount: 0,
      createdOrdersNeedingActivationCount: 0,
      cancelledOrdersWithGiftsCount: 0,
      buyOrdersCount: 0,
      userId: "",
      message: userLookupResult.message || "User not found.",
    };
  }

  const targetUserId = userLookupResult.userId!;

  // Query all order types using the new service
  const [
    paidOrdersResult,
    createdOrdersResult,
    cancelledOrdersResult,
    buyOrdersResult,
  ] = await Promise.all([
    queryPaidOrdersAwaitingGift({ userId: targetUserId }),
    queryCreatedOrdersNeedingActivation({ userId: targetUserId }),
    queryCancelledOrders({ userId: targetUserId }),
    queryBuyOrders({ userId: targetUserId }),
  ]);

  const paidOrdersAwaitingGift = convertDocsToOrders(paidOrdersResult.docs);
  const createdOrdersTemp = convertDocsToOrders(createdOrdersResult.docs);
  const cancelledOrdersTemp = convertDocsToOrders(cancelledOrdersResult.docs);
  const buyOrders = convertDocsToOrders(buyOrdersResult.docs);

  const createdOrdersNeedingActivation = await filterOrdersByMarketCollections(
    createdOrdersTemp
  );

  const cancelledOrdersWithGifts = await processCancelledOrdersWithGifts(
    cancelledOrdersTemp
  );

  const sellOrders = [
    ...paidOrdersAwaitingGift,
    ...createdOrdersNeedingActivation,
    ...cancelledOrdersWithGifts,
  ];
  const allOrders = [...sellOrders, ...buyOrders];

  return {
    success: true,
    orders: allOrders,
    sellOrders,
    paidOrdersAwaitingGift, // paid orders waiting for gift
    createdOrdersNeedingActivation, // created orders for MARKET collections
    cancelledOrdersWithGifts, // cancelled orders with gifts for refund
    buyOrders,
    count: allOrders.length,
    sellOrdersCount: sellOrders.length,
    paidOrdersAwaitingGiftCount: paidOrdersAwaitingGift.length,
    createdOrdersNeedingActivationCount: createdOrdersNeedingActivation.length,
    cancelledOrdersWithGiftsCount: cancelledOrdersWithGifts.length,
    buyOrdersCount: buyOrders.length,
    userId: targetUserId,
    message: "Orders retrieved successfully.",
  };
}

export async function completePurchase(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  await orderDoc.ref.update({
    status: OrderStatus.FULFILLED,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  return {
    success: true,
    message: "Purchase completed successfully.",
    order: {
      ...order,
      status: OrderStatus.FULFILLED,
    },
  };
}

export async function sendGiftToRelayer(params: {
  orderId: string;
  gift: GiftFromBot;
  botToken: string;
}) {
  const { orderId, gift, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Get seller's telegram ID for gift ownership
  let sellerTgId: string | undefined;
  if (order.sellerId) {
    const sellerDoc = await db
      .collection(APP_USERS_COLLECTION)
      .doc(order.sellerId)
      .get();

    if (sellerDoc.exists) {
      const seller = sellerDoc.data() as UserEntity;
      sellerTgId = seller.tg_id;
    }
  }

  if (!sellerTgId) {
    throw new Error("Seller telegram ID not found");
  }

  // Handle two different order statuses
  if (order.status === OrderStatus.PAID) {
    // Logic for paid orders - send gift to relayer with accounting
    const batch = db.batch();

    // Create gift in separate collection
    const giftId = await createGift(
      gift,
      sellerTgId,
      order.collectionId,
      batch
    );

    // Update order status and reference gift by ID
    batch.update(orderDoc.ref, {
      giftId,
      gift, // Keep for backward compatibility
      status: OrderStatus.GIFT_SENT_TO_RELAYER,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    // Transfer funds from buyer's locked balance to seller
    if (order.buyerId && order.sellerId) {
      // Calculate net amount using order's fee structure
      const purchaseFeeRate = order.fees?.purchase_fee || 0;
      const netSellerAmount = safeMultiply(
        order.price,
        1 - purchaseFeeRate / 10000
      ); // Convert BPS to decimal

      // Use reusable function for money transfer
      await transferNetAmountToSeller(order, netSellerAmount, order.buyerId);
    }

    await batch.commit();

    return {
      success: true,
      message: "Gift sent to relayer successfully.",
      order: {
        ...order,
        giftId,
        gift,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  } else if (order.status === OrderStatus.CREATED) {
    // New logic for created orders - just update with gift and activate
    // Create gift in separate collection
    const giftId = await createGift(gift, sellerTgId, order.collectionId);

    await orderDoc.ref.update({
      giftId,
      gift, // Keep for backward compatibility
      status: OrderStatus.ACTIVE,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    return {
      success: true,
      message: "Gift added and order activated successfully.",
      order: {
        ...order,
        giftId,
        gift,
        status: OrderStatus.ACTIVE,
      },
    };
  } else {
    throwInvalidOrderStatus();
  }
}
