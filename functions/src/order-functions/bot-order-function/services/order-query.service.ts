import * as admin from "firebase-admin";
import {
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
} from "../../../mikerudenko/marketplace-shared";

export interface OrderQueryParams {
  userId: string;
}

export interface OrderQueryResult {
  docs: admin.firestore.QueryDocumentSnapshot[];
}

// augment-refactor, this service file should be in common services folder
export async function queryPaidOrdersAwaitingGift(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.PAID)
    .get();

  return { docs: query.docs };
}

export async function queryCreatedOrdersNeedingActivation(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.CREATED)
    .get();

  return { docs: query.docs };
}

export async function queryCancelledOrders(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.CANCELLED)
    .get();

  return { docs: query.docs };
}

export async function queryBuyOrders(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("buyerId", "==", params.userId)
    .where("status", "==", OrderStatus.GIFT_SENT_TO_RELAYER)
    .get();

  return { docs: query.docs };
}

// augment-refactor this file should be in some utils, and use generic to pass entity there, it should be collection agnostic
export function convertDocsToOrders(
  docs: admin.firestore.QueryDocumentSnapshot[]
): OrderEntity[] {
  return docs.map((doc) => ({ id: doc.id, ...doc.data() } as OrderEntity));
}
