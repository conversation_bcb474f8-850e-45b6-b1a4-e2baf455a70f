import * as admin from "firebase-admin";
import {
  COLLECTION_NAME,
  CollectionEntity,
  CollectionStatus,
  OrderEntity,
} from "../../../mikerudenko/marketplace-shared";

// augment-refactor, this service file should be in common services folder
export async function getMarketCollectionIds(
  collectionIds: string[]
): Promise<Set<string>> {
  if (collectionIds.length === 0) {
    return new Set();
  }

  const db = admin.firestore();
  const marketCollections = new Set<string>();

  const collectionsQuery = await db
    .collection(COLLECTION_NAME)
    .where(admin.firestore.FieldPath.documentId(), "in", collectionIds)
    .get();

  collectionsQuery.forEach((doc) => {
    const collection = doc.data() as CollectionEntity;
    if (collection.status === CollectionStatus.MARKET) {
      marketCollections.add(doc.id);
    }
  });

  return marketCollections;
}

export async function filterOrdersByMarketCollections(
  orders: OrderEntity[]
): Promise<OrderEntity[]> {
  if (orders.length === 0) {
    return [];
  }

  const collectionIds = [...new Set(orders.map((order) => order.collectionId))];
  const marketCollections = await getMarketCollectionIds(collectionIds);
  return orders.filter((order) => marketCollections.has(order.collectionId));
}

export function extractCollectionIds(orders: OrderEntity[]): string[] {
  return [...new Set(orders.map((order) => order.collectionId))];
}
