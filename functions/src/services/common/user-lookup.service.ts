import * as admin from "firebase-admin";
import {
  APP_USERS_COLLECTION,
  UserEntity,
} from "../../mikerudenko/marketplace-shared";

export interface UserLookupParams {
  userId?: string;
  tgId?: string;
}

export interface UserLookupResult {
  success: boolean;
  userId?: string;
  message?: string;
}

export async function findUserIdByTgId(
  tgId: string
): Promise<UserLookupResult> {
  const db = admin.firestore();
  const usersQuery = await db
    .collection(APP_USERS_COLLECTION)
    .where("tg_id", "==", tgId)
    .limit(1)
    .get();

  if (usersQuery.empty) {
    return {
      success: false,
      message: "User not found with the provided Telegram ID.",
    };
  }

  return {
    success: true,
    userId: usersQuery.docs[0].id,
  };
}

export async function resolveUserId(
  params: UserLookupParams
): Promise<UserLookupResult> {
  const { userId, tgId } = params;

  if (userId) {
    return {
      success: true,
      userId,
    };
  }

  if (tgId) {
    return await findUserIdByTgId(tgId);
  }

  return {
    success: false,
    message: "Either userId or tgId is required.",
  };
}

export async function getUserById(userId: string): Promise<UserEntity | null> {
  const db = admin.firestore();
  const userDoc = await db.collection(APP_USERS_COLLECTION).doc(userId).get();

  if (!userDoc.exists) {
    return null;
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}

export async function getUserTelegramIds(order: {
  buyerId?: string;
  sellerId?: string;
}): Promise<{
  buyer_tg_id?: string;
  seller_tg_id?: string;
}> {
  const result: { buyer_tg_id?: string; seller_tg_id?: string } = {};

  if (order.buyerId) {
    const buyer = await getUserById(order.buyerId);
    if (buyer?.tg_id) {
      result.buyer_tg_id = buyer.tg_id;
    }
  }

  if (order.sellerId) {
    const seller = await getUserById(order.sellerId);
    if (seller?.tg_id) {
      result.seller_tg_id = seller.tg_id;
    }
  }

  return result;
}
