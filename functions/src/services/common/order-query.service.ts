import * as admin from "firebase-admin";
import {
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
} from "../../mikerudenko/marketplace-shared";

export interface OrderQueryParams {
  userId: string;
}

export interface OrderQueryResult {
  docs: admin.firestore.QueryDocumentSnapshot[];
}

export async function queryPaidOrdersAwaitingGift(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.PAID)
    .get();

  return { docs: query.docs };
}

export async function queryCreatedOrdersNeedingActivation(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.CREATED)
    .get();

  return { docs: query.docs };
}

export async function queryCancelledOrders(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.CANCELLED)
    .get();

  return { docs: query.docs };
}

export async function queryBuyOrders(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("buyerId", "==", params.userId)
    .where("status", "==", OrderStatus.GIFT_SENT_TO_RELAYER)
    .get();

  return { docs: query.docs };
}

export async function queryOrderById(orderId: string): Promise<admin.firestore.DocumentSnapshot> {
  const db = admin.firestore();
  return await db.collection(ORDERS_COLLECTION_NAME).doc(orderId).get();
}

export function convertDocsToOrders(
  docs: admin.firestore.QueryDocumentSnapshot[]
): OrderEntity[] {
  return docs.map((doc) => ({ id: doc.id, ...doc.data() } as OrderEntity));
}
