import * as admin from "firebase-admin";
import { 
  GiftEntity, 
  GiftStatus, 
  OrderGift, 
  GIFTS_COLLECTION_NAME,
  AppDate 
} from "../mikerudenko/marketplace-shared";

/**
 * Creates a new gift document in the gifts collection
 */
export async function createGift(
  gift: OrderGift,
  ownerTgId: string,
  collectionId: string,
  batch?: admin.firestore.WriteBatch
): Promise<string> {
  const db = admin.firestore();
  const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc();
  
  const giftEntity: GiftEntity = {
    id: giftRef.id,
    base_name: gift.base_name,
    owned_gift_id: gift.owned_gift_id,
    owner_tg_id: ownerTgId,
    collectionId,
    status: GiftStatus.DEPOSITED,
    backdrop: gift.backdrop,
    model: gift.model,
    symbol: gift.symbol,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };

  if (batch) {
    batch.set(giftRef, giftEntity);
  } else {
    await giftRef.set(giftEntity);
  }

  return giftRef.id;
}

/**
 * Updates gift ownership and status
 */
export async function updateGiftOwnership(
  giftId: string,
  newOwnerTgId: string,
  status: GiftStatus,
  batch?: admin.firestore.WriteBatch
): Promise<void> {
  const db = admin.firestore();
  const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc(giftId);

  const updateData = {
    owner_tg_id: newOwnerTgId,
    status,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };

  if (batch) {
    batch.update(giftRef, updateData);
  } else {
    await giftRef.update(updateData);
  }
}

/**
 * Gets a gift by ID
 */
export async function getGiftById(giftId: string): Promise<GiftEntity | null> {
  const db = admin.firestore();
  const giftDoc = await db.collection(GIFTS_COLLECTION_NAME).doc(giftId).get();
  
  if (!giftDoc.exists) {
    return null;
  }

  return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
}

/**
 * Gets gifts by owner telegram ID
 */
export async function getGiftsByOwner(ownerTgId: string): Promise<GiftEntity[]> {
  const db = admin.firestore();
  const snapshot = await db
    .collection(GIFTS_COLLECTION_NAME)
    .where("owner_tg_id", "==", ownerTgId)
    .get();

  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  })) as GiftEntity[];
}

/**
 * Marks a gift as withdrawn
 */
export async function markGiftAsWithdrawn(
  giftId: string,
  batch?: admin.firestore.WriteBatch
): Promise<void> {
  const db = admin.firestore();
  const giftRef = db.collection(GIFTS_COLLECTION_NAME).doc(giftId);

  const updateData = {
    status: GiftStatus.WITHDRAWN,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as AppDate,
  };

  if (batch) {
    batch.update(giftRef, updateData);
  } else {
    await giftRef.update(updateData);
  }
}

/**
 * Converts OrderGift to GiftEntity format (for backward compatibility)
 */
export function orderGiftToGiftEntity(
  orderGift: OrderGift,
  ownerTgId: string,
  collectionId: string
): Omit<GiftEntity, 'id' | 'createdAt' | 'updatedAt'> {
  return {
    base_name: orderGift.base_name,
    owned_gift_id: orderGift.owned_gift_id,
    owner_tg_id: ownerTgId,
    collectionId,
    status: GiftStatus.DEPOSITED,
    backdrop: orderGift.backdrop,
    model: orderGift.model,
    symbol: orderGift.symbol,
  };
}

/**
 * Converts GiftEntity to OrderGift format (for backward compatibility)
 */
export function giftEntityToOrderGift(giftEntity: GiftEntity): OrderGift {
  return {
    base_name: giftEntity.base_name,
    owned_gift_id: giftEntity.owned_gift_id,
    backdrop: giftEntity.backdrop,
    model: giftEntity.model,
    symbol: giftEntity.symbol,
  };
}
