import { HttpsError } from "firebase-functions/v2/https";
import { AUTH_ERRORS } from "../error-messages";
import { UserEntity } from "../mikerudenko/marketplace-shared";

// augment-refactor, this 2 functions, are the same - leave only one function

export function requireTelegramId(user: UserEntity): void {
  if (!user.tg_id) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TELEGRAM_ID_REQUIRED,
        fallbackMessage:
          "User must have a Telegram ID configured to perform this operation.",
      })
    );
  }
}

export function validateTelegramIdForOrderOperation(
  user: UserEntity,
  operation: string
): void {
  if (!user.tg_id) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TELEGRAM_ID_REQUIRED,
        fallbackMessage: `User must have a Telegram ID configured to ${operation}.`,
      })
    );
  }
}
