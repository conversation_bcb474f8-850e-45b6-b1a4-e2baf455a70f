import { HttpsError } from "firebase-functions/v2/https";
import { AUTH_ERRORS } from "../error-messages";
import { UserEntity } from "../mikerudenko/marketplace-shared";

/**
 * Validates that a user has a telegram ID configured.
 * This is required for all order-related operations to ensure
 * users can receive notifications and interact with the bot.
 */
export function requireTelegramId(user: UserEntity): void {
  if (!user.tg_id) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TELEGRAM_ID_REQUIRED,
        fallbackMessage: "User must have a Telegram ID configured to perform this operation.",
      })
    );
  }
}

/**
 * Validates that a user has a telegram ID for order operations.
 * This is a unified validation function used across all order flows.
 */
export function validateTelegramIdForOrderOperation(
  user: UserEntity,
  operation: string
): void {
  if (!user.tg_id) {
    throw new HttpsError(
      "failed-precondition",
      JSON.stringify({
        errorKey: AUTH_ERRORS.TELEGRAM_ID_REQUIRED,
        fallbackMessage: `User must have a Telegram ID configured to ${operation}.`,
      })
    );
  }
}
