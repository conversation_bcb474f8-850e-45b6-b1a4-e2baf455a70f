import * as admin from "firebase-admin";
import {
  GIFTS_COLLECTION_NAME,
  GiftEntity,
} from "../../mikerudenko/marketplace-shared";
import { verifyBotToken } from "../../services/bot-auth-service/bot-auth-service";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwGiftIdRequired,
} from "./get-gift-by-id-function.error-handler";

function validateGiftId(giftId: string) {
  if (!giftId) {
    throwGiftIdRequired();
  }
}

// AUGMENT-REFACTOR, we have this validation in other bot specific function, adhere dry principle
function validateBotToken(botToken: string) {
  if (!botToken) {
    throwBotTokenRequired();
  }

  if (!verifyBotToken(botToken)) {
    throwInvalidBotToken();
  }
}

export async function getGiftById(params: {
  giftId: string;
  botToken: string;
}): Promise<GiftEntity | null> {
  const { giftId, botToken } = params;

  validateGiftId(giftId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const giftDoc = await db.collection(GIFTS_COLLECTION_NAME).doc(giftId).get();

  if (!giftDoc.exists) {
    return null;
  }

  return { id: giftDoc.id, ...giftDoc.data() } as GiftEntity;
}
