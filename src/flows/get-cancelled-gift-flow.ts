import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { clearUserSession } from "../services/session";
import {
  getBusinessConnectionId,
  getOrderById,
  transferGift,
} from "../utils/business-connection-helpers";
import { resetGiftOnCancelledOrder } from "../firebase-service";
import { OrderStatus } from "../mikerudenko/marketplace-shared";
import {
  logCancelledGiftProcessingStarted,
  logOrderNotFoundForCancelledGift,
  logOrderNotCancelled,
  logUserNotAuthorizedForCancelledGift,
  logNoGiftToTransferBack,
  logMissingBusinessConnectionForCancelledGift,
  logCancelledGiftTransferStarted,
  logGiftFieldResetSuccess,
  logGiftFieldResetError,
  logCancelledGiftTransferCompleted,
} from "./loggers/get-cancelled-gift-flow.logger";
import { getOrderOwnedGiftId } from "@/services/gift-service";

export interface FlowContext {
  ctx: Context;
  chat_id: number;
  userId: string;
  pendingOrderId: string;
}

export const handleGetCancelledGift = async (
  flowContext: FlowContext
): Promise<boolean> => {
  const { ctx, chat_id, userId, pendingOrderId } = flowContext;

  logCancelledGiftProcessingStarted({
    chat_id,
    userId,
    pendingOrderId,
  });

  // Get order and validate
  const order = await getOrderById(pendingOrderId);
  if (!order) {
    logOrderNotFoundForCancelledGift({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.ORDER_NOT_FOUND
    );
    return false;
  }

  // Check if order is actually cancelled
  if (order.status !== OrderStatus.CANCELLED) {
    logOrderNotCancelled({
      chat_id,
      userId,
      pendingOrderId,
      orderStatus: order.status,
    });
    return false;
  }

  // Validate that current user is the original seller (who sent the gift)
  if (order.seller_tg_id !== userId) {
    logUserNotAuthorizedForCancelledGift({
      chat_id,
      userId,
      pendingOrderId,
      originalSellerId: order.seller_tg_id as string,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      "You are not authorized to retrieve this gift. Only the original seller can claim cancelled gifts."
    );
    return false;
  }

  const giftToTransferBack = await getOrderOwnedGiftId(order);

  if (!giftToTransferBack) {
    logNoGiftToTransferBack({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.NO_GIFT_TO_TRANSFER
    );
    return false;
  }

  const businessConnectionId = getBusinessConnectionId(ctx);
  if (!businessConnectionId) {
    logMissingBusinessConnectionForCancelledGift({
      chat_id,
      userId,
      pendingOrderId,
    });
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_MISSING_INFO
    );
    return false;
  }

  logCancelledGiftTransferStarted({
    chat_id,
    userId,
    pendingOrderId,
    businessConnectionId,
    giftToTransferBack,
  });

  await transferGift(ctx, businessConnectionId, chat_id, giftToTransferBack);

  // Call the cloud function to reset gift field after successful transfer
  try {
    await resetGiftOnCancelledOrder({
      userId,
      botToken: process.env.BOT_TOKEN || "",
      orderId: pendingOrderId,
    });
    logGiftFieldResetSuccess({
      orderId: pendingOrderId,
    });
  } catch (error) {
    logGiftFieldResetError({
      orderId: pendingOrderId,
      error,
    });
  }

  await clearUserSession(userId);

  logCancelledGiftTransferCompleted({
    chat_id,
    userId,
    pendingOrderId,
  });

  return true;
};
