import axios from "axios";
import { log } from "../utils/logger";
import { loadEnvironment } from "../config/env-loader";
import type { GiftEntity, OrderGift } from "../mi<PERSON><PERSON><PERSON>/marketplace-shared";

loadEnvironment();

const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;
const FIREBASE_REGION = process.env.FIREBASE_REGION ?? "europe-central2";
const BOT_TOKEN = process.env.BOT_TOKEN;

if (!FIREBASE_PROJECT_ID) {
  throw new Error("FIREBASE_PROJECT_ID is required in environment variables");
}

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const FIREBASE_FUNCTIONS_BASE_URL = `https://${FIREBASE_REGION}-${FIREBASE_PROJECT_ID}.cloudfunctions.net`;

export async function getGiftById(giftId: string): Promise<GiftEntity | null> {
  try {
    const response = await axios.post(
      `${FIREBASE_FUNCTIONS_BASE_URL}/getGiftByIdByBot`,
      {
        data: {
          giftId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    log.error("Error getting gift by ID", error, {
      operation: "get_gift_by_id",
      giftId,
    });
    return null;
  }
}

/**
 * Converts GiftEntity to OrderGift format for backward compatibility
 */
export function giftEntityToOrderGift(giftEntity: GiftEntity): OrderGift {
  return {
    base_name: giftEntity.base_name,
    owned_gift_id: giftEntity.owned_gift_id,
    backdrop: giftEntity.backdrop,
    model: giftEntity.model,
    symbol: giftEntity.symbol,
  };
}

/**
 * Gets gift data for an order, handling both embedded gifts and gift collection
 */
export async function getOrderGift(order: {
  gift?: OrderGift | null;
  giftId?: string | null;
}): Promise<OrderGift | null> {
  if (order.gift) {
    return order.gift;
  }

  // If order has giftId, fetch from gifts collection
  if (order.giftId) {
    const giftEntity = await getGiftById(order.giftId);
    return giftEntity ?? null;
  }

  return null;
}

const giftCache = new Map<string, OrderGift>();

/**
 * Cached version of getOrderGift for better performance
 */
export async function getOrderGiftCached(order: {
  gift?: OrderGift | null;
  giftId?: string | null;
}): Promise<OrderGift | null> {
  // If order has embedded gift, use it (backward compatibility)
  if (order.gift) {
    return order.gift;
  }

  // If order has giftId, check cache first
  if (order.giftId) {
    if (giftCache.has(order.giftId)) {
      return giftCache.get(order.giftId)!;
    }

    const giftEntity = await getGiftById(order.giftId);
    if (giftEntity) {
      const orderGift = giftEntityToOrderGift(giftEntity);
      giftCache.set(order.giftId, orderGift);
      return orderGift;
    }
  }

  return null;
}
