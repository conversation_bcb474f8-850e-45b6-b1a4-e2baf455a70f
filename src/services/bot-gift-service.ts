import { OrderGift } from "../mikerudenko/marketplace-shared";
import { getOrderGiftCached } from "./gift-service";

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  buyer_tg_id?: string;
  seller_tg_id?: string;
  status: string;
  giftId?: string | null;
  [key: string]: any;
}

export async function getOrderOwnedGiftId(
  order: OrderData
): Promise<string | null> {
  if (order.giftId) {
    const gift = await getOrderGiftCached(order);
    return gift?.owned_gift_id || null;
  }

  return null;
}

/**
 * Gets the full gift object for an order, handling both embedded gifts and gift collection
 */
export async function getOrderGiftForBot(
  order: OrderData
): Promise<OrderGift | null> {
  return await getOrderGiftCached(order);
}

/**
 * Checks if an order has any gift (embedded or referenced)
 */
export function hasOrderGift(order: OrderData): boolean {
  return !!(order.gift || order.giftId);
}

/**
 * Gets the gift data needed for transfer operations
 */
export async function getGiftForTransfer(order: OrderData): Promise<{
  ownedGiftId: string;
  fullGift: OrderGift;
} | null> {
  const ownedGiftId = await getOrderOwnedGiftId(order);
  const fullGift = await getOrderGiftForBot(order);

  if (!ownedGiftId || !fullGift) {
    return null;
  }

  return {
    ownedGiftId,
    fullGift,
  };
}
